#!/usr/bin/env python
"""
测试CrewAI LLM的正确调用方式
"""

import os
import sys
from pathlib import Path
import time

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 设置环境变量
os.environ["OTEL_PYTHON_DISABLED"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()
config_service.setup_llm_env()

def test_crewai_llm_methods():
    """测试CrewAI LLM的不同调用方法"""
    print("🔧 测试CrewAI LLM调用方法...")
    
    try:
        from tools.llm_client import LLMClient
        
        print("📦 初始化LLM客户端...")
        client = LLMClient()
        llm = client.get_llm()
        
        print("🤖 测试LLM实例的可用方法...")
        methods = [attr for attr in dir(llm) if not attr.startswith('_')]
        print(f"可用方法: {methods}")
        
        # 测试不同的调用方式
        test_prompt = "你好，请回复'测试成功'"
        
        print("\n📝 测试方法1: llm.call()")
        try:
            start_time = time.time()
            result1 = llm.call(test_prompt)
            end_time = time.time()
            print(f"✅ call()方法成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📄 结果类型: {type(result1)}")
            print(f"📄 结果内容: {result1}")
        except Exception as e:
            print(f"❌ call()方法失败: {str(e)}")
        
        print("\n📝 测试方法2: llm.invoke()")
        try:
            start_time = time.time()
            result2 = llm.invoke(test_prompt)
            end_time = time.time()
            print(f"✅ invoke()方法成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📄 结果类型: {type(result2)}")
            print(f"📄 结果内容: {result2}")
        except Exception as e:
            print(f"❌ invoke()方法失败: {str(e)}")
            
        print("\n📝 测试方法3: 直接调用 llm()")
        try:
            start_time = time.time()
            result3 = llm(test_prompt)
            end_time = time.time()
            print(f"✅ 直接调用成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📄 结果类型: {type(result3)}")
            print(f"📄 结果内容: {result3}")
        except Exception as e:
            print(f"❌ 直接调用失败: {str(e)}")
            
        # 测试消息格式调用
        print("\n📝 测试方法4: 消息格式调用")
        try:
            from langchain.schema import HumanMessage
            messages = [HumanMessage(content=test_prompt)]
            start_time = time.time()
            result4 = llm.invoke(messages)
            end_time = time.time()
            print(f"✅ 消息格式调用成功，耗时: {end_time - start_time:.2f}秒")
            print(f"📄 结果类型: {type(result4)}")
            print(f"📄 结果内容: {result4}")
        except Exception as e:
            print(f"❌ 消息格式调用失败: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始CrewAI LLM调用方法测试\n")
    
    success = test_crewai_llm_methods()
    
    print("\n" + "="*50)
    if success:
        print("🎉 测试完成！")
    else:
        print("⚠️ 测试失败！")
    print("="*50)
