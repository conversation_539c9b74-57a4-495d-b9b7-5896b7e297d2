#!/usr/bin/env python
"""
测试LLM调用的具体实现
"""

import os
import sys
from pathlib import Path
import time

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 设置环境变量
os.environ["OTEL_PYTHON_DISABLED"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()
config_service.setup_llm_env()

def test_llm_call():
    """测试LLM调用"""
    print("🔧 测试LLM调用...")
    
    try:
        from tools.llm_client import LLMClient
        
        print("📦 初始化LLM客户端...")
        client = LLMClient()
        llm = client.get_llm()
        
        print("🤖 测试LLM调用...")
        test_prompt = "你好，请简单回复'测试成功'"
        
        print(f"📝 发送提示: {test_prompt}")
        print("⏱️ 开始调用...")
        
        start_time = time.time()
        
        # 使用CrewAI LLM的call方法
        result = llm.call(test_prompt)
        
        end_time = time.time()
        
        print(f"✅ LLM调用成功！")
        print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
        print(f"📄 结果类型: {type(result)}")
        print(f"📄 结果内容: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM调用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_base_agent_call():
    """测试BaseAgent的call_llm方法"""
    print("\n🔧 测试BaseAgent的call_llm方法...")
    
    try:
        from agents.base_agent import BaseAgent
        
        # 创建一个简单的测试智能体
        class TestAgent(BaseAgent):
            def __init__(self):
                super().__init__("测试智能体", "测试用智能体")
        
        print("📦 初始化测试智能体...")
        agent = TestAgent()
        
        print("🤖 测试call_llm方法...")
        test_prompt = "你好，请简单回复'测试成功'"
        
        print(f"📝 发送提示: {test_prompt}")
        print("⏱️ 开始调用...")
        
        start_time = time.time()
        
        # 使用BaseAgent的call_llm方法
        result = agent.call_llm(test_prompt)
        
        end_time = time.time()
        
        print(f"✅ BaseAgent调用成功！")
        print(f"⏱️ 耗时: {end_time - start_time:.2f} 秒")
        print(f"📄 结果类型: {type(result)}")
        print(f"📄 结果内容: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ BaseAgent调用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始LLM调用测试\n")
    
    # 测试直接LLM调用
    direct_ok = test_llm_call()
    
    # 测试BaseAgent调用
    agent_ok = test_base_agent_call()
    
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"直接LLM调用: {'✅ 正常' if direct_ok else '❌ 失败'}")
    print(f"BaseAgent调用: {'✅ 正常' if agent_ok else '❌ 失败'}")
    
    if direct_ok and agent_ok:
        print("\n🎉 所有测试通过，LLM调用正常！")
    else:
        print("\n⚠️ 存在问题，需要进一步调试")
    
    print("="*50)
