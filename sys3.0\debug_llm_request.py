#!/usr/bin/env python
"""
调试LLM请求的具体差异
"""

import os
import sys
from pathlib import Path
import time
import json

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 设置环境变量
os.environ["OTEL_PYTHON_DISABLED"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()
config_service.setup_llm_env()

def test_direct_api():
    """测试直接API调用"""
    print("🔧 测试直接API调用...")
    
    # 获取配置
    llm_config = config_service.get_config("llm", as_dict=True)
    
    try:
        import requests
        
        url = f"{llm_config.get('base_url')}/chat/completions"
        headers = {
            "Authorization": f"Bearer {llm_config.get('api_key')}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": llm_config.get('model'),
            "messages": [
                {"role": "user", "content": "你好，请回复'测试成功'"}
            ],
            "max_tokens": 50,
            "temperature": 0.1
        }
        
        print(f"📡 请求URL: {url}")
        print(f"📄 请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print(f"📄 请求体: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=data, headers=headers, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 直接API调用成功！")
            print(f"📝 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 直接API调用失败，状态码: {response.status_code}")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 直接API调用异常: {str(e)}")
        return False

def test_crewai_llm_config():
    """测试CrewAI LLM的配置"""
    print("\n🔧 测试CrewAI LLM配置...")
    
    try:
        from tools.llm_client import LLMClient
        
        client = LLMClient()
        llm = client.get_llm()
        
        print(f"📄 LLM配置信息:")
        print(f"  - model: {llm.model}")
        print(f"  - api_key: {llm.api_key[:10]}...")
        print(f"  - base_url: {llm.base_url}")
        print(f"  - temperature: {llm.temperature}")
        print(f"  - max_tokens: {llm.max_tokens}")
        print(f"  - timeout: {getattr(llm, 'timeout', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"❌ CrewAI LLM配置检查失败: {str(e)}")
        return False

def test_litellm_direct():
    """测试LiteLLM直接调用"""
    print("\n🔧 测试LiteLLM直接调用...")
    
    try:
        import litellm
        
        # 获取配置
        llm_config = config_service.get_config("llm", as_dict=True)
        
        # 设置LiteLLM参数
        model_name = f"openai/{llm_config.get('model')}"
        
        print(f"📄 LiteLLM调用参数:")
        print(f"  - model: {model_name}")
        print(f"  - api_key: {llm_config.get('api_key')[:10]}...")
        print(f"  - api_base: {llm_config.get('base_url')}")
        
        # 直接调用LiteLLM
        response = litellm.completion(
            model=model_name,
            messages=[{"role": "user", "content": "你好，请回复'测试成功'"}],
            api_key=llm_config.get('api_key'),
            api_base=llm_config.get('base_url'),
            temperature=0.1,
            max_tokens=50,
            timeout=120
        )
        
        print(f"✅ LiteLLM直接调用成功！")
        print(f"📝 响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ LiteLLM直接调用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始LLM请求调试\n")
    
    # 测试直接API调用
    direct_ok = test_direct_api()
    
    # 测试CrewAI LLM配置
    config_ok = test_crewai_llm_config()
    
    # 测试LiteLLM直接调用
    litellm_ok = test_litellm_direct()
    
    print("\n" + "="*50)
    print("📊 调试结果汇总:")
    print(f"直接API调用: {'✅ 正常' if direct_ok else '❌ 失败'}")
    print(f"CrewAI配置: {'✅ 正常' if config_ok else '❌ 失败'}")
    print(f"LiteLLM调用: {'✅ 正常' if litellm_ok else '❌ 失败'}")
    print("="*50)
