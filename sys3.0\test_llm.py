#!/usr/bin/env python
"""
LLM连接测试脚本
用于测试阿里云千问API的连接和响应
"""

import os
import sys
from pathlib import Path
import time

# 添加项目根目录到 Python 路径
ROOT_DIR = Path(__file__).parent
sys.path.insert(0, str(ROOT_DIR))

# 设置环境变量
os.environ["OTEL_PYTHON_DISABLED"] = "true"
os.environ["DISABLE_TELEMETRY"] = "true"

# 导入配置服务
from config.config import config_service

# 加载配置
config_service.load_config()
config_service.setup_llm_env()

def test_api_connection():
    """测试API连接"""
    print("🔧 测试阿里云千问API连接...")
    
    # 获取配置
    llm_config = config_service.get_config("llm", as_dict=True)
    print(f"API Base: {llm_config.get('base_url')}")
    print(f"Model: {llm_config.get('model')}")
    print(f"API Key: {llm_config.get('api_key')[:10]}...")
    
    try:
        # 使用requests直接测试API
        import requests
        
        url = f"{llm_config.get('base_url')}/chat/completions"
        headers = {
            "Authorization": f"Bearer {llm_config.get('api_key')}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": llm_config.get('model'),
            "messages": [
                {"role": "user", "content": "你好，请回复'测试成功'"}
            ],
            "max_tokens": 50,
            "temperature": 0.1
        }
        
        print("📡 发送测试请求...")
        start_time = time.time()
        
        response = requests.post(url, json=data, headers=headers, timeout=60)
        
        end_time = time.time()
        print(f"⏱️ 响应时间: {end_time - start_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ API连接成功！")
            print(f"📝 响应内容: {content}")
            return True
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，可能是网络问题或API响应太慢")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return False

def test_llm_client():
    """测试LLM客户端"""
    print("\n🔧 测试LLM客户端...")
    
    try:
        from tools.llm_client import LLMClient
        
        print("📦 初始化LLM客户端...")
        client = LLMClient()
        
        print("🤖 获取LLM实例...")
        llm = client.get_llm()
        
        print("✅ LLM客户端初始化成功！")
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始LLM连接测试\n")
    
    # 测试API连接
    api_ok = test_api_connection()
    
    # 测试LLM客户端
    client_ok = test_llm_client()
    
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"API连接: {'✅ 正常' if api_ok else '❌ 失败'}")
    print(f"LLM客户端: {'✅ 正常' if client_ok else '❌ 失败'}")
    
    if api_ok and client_ok:
        print("\n🎉 所有测试通过，LLM配置正常！")
    else:
        print("\n⚠️ 存在问题，请检查配置和网络连接")
    
    print("="*50)
