2025-07-30 13:17:54 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 13:17:54 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cuda
2025-07-30 13:17:54 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\93062\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 13:17:59 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\93062\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 13:18:01 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cuda
2025-07-30 13:18:02 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 13:18:02 - LawRAGEngine - INFO - 从 D:\Users\93062\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 13:18:02 - faiss.loader - INFO - Loading faiss.
2025-07-30 13:18:02 - faiss.loader - INFO - Successfully loaded faiss.
2025-07-30 13:18:02 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 13:18:03 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-30 13:18:03 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 13:18:05 - Neo4jConnection - INFO - 成功连接到Neo4j数据库
2025-07-30 13:18:05 - smart_graph_loader - INFO - ✅ 数据库连接成功
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: HazardBehavior)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:HazardBehavior)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: WarningSignal)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:WarningSignal)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: TriggerCause)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:TriggerCause)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Damage)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:Damage)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - smart_graph_loader - INFO - 📊 文件未变化，无需更新
2025-07-30 13:18:06 - smart_graph_loader - INFO - 📋 更新检查结果: 数据库状态正常，无需更新
2025-07-30 13:18:06 - smart_graph_loader - INFO - ✅ 图谱数据已是最新，无需更新
2025-07-30 13:18:06 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:18:06 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:18:10 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 13:18:12 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 13:18:14 - system - INFO - 系统信息: Python 3.12.3 | packaged by conda-forge | (main, Apr 15 2024, 18:20:11) [MSC v.1938 64 bit (AMD64)], OS: Windows
2025-07-30 13:18:14 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 13:18:14 - system - INFO - 初始化控制器智能体..
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 13:18:14 - system - INFO - 开始执行任务，任务ID: TASK-20250730-13, 模式: extract
2025-07-30 13:18:14 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-13
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化智能体
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 13:18:14 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 13:18:14 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 13:18:14 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 13:18:14 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 13:18:14 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:18:36 - openai._base_client - INFO - Retrying request to /chat/completions in 0.457666 seconds
2025-07-30 13:18:58 - openai._base_client - INFO - Retrying request to /chat/completions in 0.877048 seconds
2025-07-30 13:19:20 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:19:20 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:19:20 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:19:20 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:19:20 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:19:20 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 13:19:20 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 13:19:20 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:19:41 - openai._base_client - INFO - Retrying request to /chat/completions in 0.432711 seconds
2025-07-30 13:20:02 - openai._base_client - INFO - Retrying request to /chat/completions in 0.917958 seconds
2025-07-30 13:20:24 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:20:24 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:20:24 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:20:24 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:20:24 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:20:24 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 13:20:24 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 13:20:24 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:22:55 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 13:22:55 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cuda
2025-07-30 13:22:55 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\93062\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 13:23:02 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\93062\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 13:23:05 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cuda
2025-07-30 13:23:05 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 13:23:05 - LawRAGEngine - INFO - 从 D:\Users\93062\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 13:23:05 - faiss.loader - INFO - Loading faiss.
2025-07-30 13:23:06 - faiss.loader - INFO - Successfully loaded faiss.
2025-07-30 13:23:06 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 13:23:06 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-30 13:23:07 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 13:23:09 - Neo4jConnection - INFO - 成功连接到Neo4j数据库
2025-07-30 13:23:09 - smart_graph_loader - INFO - ✅ 数据库连接成功
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: HazardBehavior)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:HazardBehavior)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: WarningSignal)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:WarningSignal)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: TriggerCause)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:TriggerCause)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Damage)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:Damage)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - smart_graph_loader - INFO - 📊 文件未变化，无需更新
2025-07-30 13:23:09 - smart_graph_loader - INFO - 📋 更新检查结果: 数据库状态正常，无需更新
2025-07-30 13:23:09 - smart_graph_loader - INFO - ✅ 图谱数据已是最新，无需更新
2025-07-30 13:23:09 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:23:09 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:23:20 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 13:23:23 - system - INFO - 系统信息: Python 3.12.3 | packaged by conda-forge | (main, Apr 15 2024, 18:20:11) [MSC v.1938 64 bit (AMD64)], OS: Windows
2025-07-30 13:23:23 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 13:23:23 - system - INFO - 初始化控制器智能体..
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 13:23:23 - system - INFO - 开始执行任务，任务ID: TASK-20250730-14, 模式: extract
2025-07-30 13:23:23 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-14
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化智能体
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 13:23:23 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 13:23:23 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 13:23:23 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 13:23:23 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 13:23:23 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:23:45 - openai._base_client - INFO - Retrying request to /chat/completions in 0.493820 seconds
2025-07-30 13:24:06 - openai._base_client - INFO - Retrying request to /chat/completions in 0.781729 seconds
2025-07-30 13:24:28 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:24:28 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:24:28 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:24:28 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:24:28 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:24:28 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 13:24:28 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 13:24:28 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:24:49 - openai._base_client - INFO - Retrying request to /chat/completions in 0.399846 seconds
2025-07-30 13:25:10 - openai._base_client - INFO - Retrying request to /chat/completions in 0.946423 seconds
2025-07-30 13:25:33 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:25:33 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:25:33 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:25:33 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:25:33 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:25:33 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 13:25:33 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 13:25:33 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:25:54 - openai._base_client - INFO - Retrying request to /chat/completions in 0.489342 seconds
2025-07-30 16:11:51 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 16:17:35 - numexpr.utils - INFO - Note: NumExpr detected 20 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 16.
2025-07-30 16:17:35 - numexpr.utils - INFO - NumExpr defaulting to 16 threads.
2025-07-30 16:17:38 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 16:17:38 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cpu
2025-07-30 16:17:38 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\Administrator\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 16:17:39 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\Administrator\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 16:17:40 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cpu
2025-07-30 16:17:40 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 16:17:40 - LawRAGEngine - INFO - 从 D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 16:17:40 - LawRAGEngine - ERROR - 加载向量存储失败: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
Traceback (most recent call last):
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 56, in dependable_faiss_import
    import faiss
ModuleNotFoundError: No module named 'faiss'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Users\Administrator\Desktop\3.01\sys3.0\tools\law_rag_engine\rag_engine.py", line 137, in __init__
    self.vectorstore = FAISS.load_local(
                       ~~~~~~~~~~~~~~~~^
        str(self.vector_store_path),
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        self.embeddings,
        ^^^^^^^^^^^^^^^^
        allow_dangerous_deserialization=True  # 添加参数，允许反序列化
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 1204, in load_local
    faiss = dependable_faiss_import()
  File "D:\anaconda3\Lib\site-packages\langchain_community\vectorstores\faiss.py", line 58, in dependable_faiss_import
    raise ImportError(
    ...<3 lines>...
    )
ImportError: Could not import faiss python package. Please install it with `pip install faiss-gpu` (for CUDA supported GPU) or `pip install faiss-cpu` (depending on Python version).
2025-07-30 16:17:40 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 16:17:40 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 16:17:44 - smart_graph_loader - ERROR - ❌ 数据库连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 16:17:55 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 16:17:57 - system - INFO - 系统信息: Python 3.13.5 | packaged by Anaconda, Inc. | (main, Jun 12 2025, 16:37:03) [MSC v.1929 64 bit (AMD64)], OS: Windows
2025-07-30 16:17:57 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 16:17:57 - system - INFO - 执行系统状态检查..
2025-07-30 16:17:57 - system - INFO - 
============================================================
2025-07-30 16:17:57 - system - INFO - = 系统运行中，将从网络收集数据
2025-07-30 16:17:57 - system - INFO - ============================================================

2025-07-30 16:17:57 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 16:18:01 - system - INFO - 系统状态检查结果:
2025-07-30 16:18:01 - system - INFO - - config: ok - 配置加载成功
2025-07-30 16:18:01 - system - INFO - - llm: ok - LLM配置有效，提供商: openai
2025-07-30 16:18:01 - system - INFO - - data_dirs: ok - 数据目录已准备就绪
2025-07-30 16:18:01 - system - ERROR - - neo4j: error - Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
2025-07-30 16:18:01 - system - WARNING - - gpu: warning - GPU不可用，使用CPU模式
2025-07-30 16:18:01 - system - ERROR - 
系统状态检查失败。以下组件存在问题:
- neo4j: Neo4j连接失败: [4000] Neo4j服务不可用: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 10061] 由于目标计算机积极拒绝，无法连接。)
- gpu: GPU不可用，使用CPU模式
