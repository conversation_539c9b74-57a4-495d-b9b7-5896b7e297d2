2025-07-30 13:17:54 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 13:17:54 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cuda
2025-07-30 13:17:54 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\93062\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 13:17:59 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\93062\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 13:18:01 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cuda
2025-07-30 13:18:02 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 13:18:02 - LawRAGEngine - INFO - 从 D:\Users\93062\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 13:18:02 - faiss.loader - INFO - Loading faiss.
2025-07-30 13:18:02 - faiss.loader - INFO - Successfully loaded faiss.
2025-07-30 13:18:02 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 13:18:03 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-30 13:18:03 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 13:18:05 - Neo4jConnection - INFO - 成功连接到Neo4j数据库
2025-07-30 13:18:05 - smart_graph_loader - INFO - ✅ 数据库连接成功
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: HazardBehavior)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:HazardBehavior)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: WarningSignal)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:WarningSignal)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: TriggerCause)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:TriggerCause)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Damage)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:Damage)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:18:06 - smart_graph_loader - INFO - 📊 文件未变化，无需更新
2025-07-30 13:18:06 - smart_graph_loader - INFO - 📋 更新检查结果: 数据库状态正常，无需更新
2025-07-30 13:18:06 - smart_graph_loader - INFO - ✅ 图谱数据已是最新，无需更新
2025-07-30 13:18:06 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:18:06 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:18:10 - httpx - INFO - HTTP Request: GET https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json "HTTP/1.1 200 OK"
2025-07-30 13:18:12 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 13:18:14 - system - INFO - 系统信息: Python 3.12.3 | packaged by conda-forge | (main, Apr 15 2024, 18:20:11) [MSC v.1938 64 bit (AMD64)], OS: Windows
2025-07-30 13:18:14 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 13:18:14 - system - INFO - 初始化控制器智能体..
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 13:18:14 - system - INFO - 开始执行任务，任务ID: TASK-20250730-13, 模式: extract
2025-07-30 13:18:14 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-13
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化智能体
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 13:18:14 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 13:18:14 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:18:14 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 13:18:14 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 13:18:14 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 13:18:14 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 13:18:14 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 13:18:14 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 13:18:14 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:18:36 - openai._base_client - INFO - Retrying request to /chat/completions in 0.457666 seconds
2025-07-30 13:18:58 - openai._base_client - INFO - Retrying request to /chat/completions in 0.877048 seconds
2025-07-30 13:19:20 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:19:20 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:19:20 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:19:20 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:19:20 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:19:20 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 13:19:20 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 13:19:20 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:19:41 - openai._base_client - INFO - Retrying request to /chat/completions in 0.432711 seconds
2025-07-30 13:20:02 - openai._base_client - INFO - Retrying request to /chat/completions in 0.917958 seconds
2025-07-30 13:20:24 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:20:24 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:20:24 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:20:24 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:20:24 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:20:24 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 13:20:24 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 13:20:24 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:22:55 - LawRAGEngine - INFO - 初始化法规检索引擎
2025-07-30 13:22:55 - LawRAGEngine - INFO - 法规RAG引擎使用设备: cuda
2025-07-30 13:22:55 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: D:\Users\93062\Desktop\3.01\sys3.0\models\paraphrase-multilingual-MiniLM-L12-v2
2025-07-30 13:23:02 - LawRAGEngine - INFO - 初始化重排序模型: D:\Users\93062\Desktop\3.01\sys3.0\models\bge-reranker-base
2025-07-30 13:23:05 - LawRAGEngine - INFO - 重排序模型初始化成功，使用设备: cuda
2025-07-30 13:23:05 - LawRAGEngine - INFO - 重排序模型预热完成
2025-07-30 13:23:05 - LawRAGEngine - INFO - 从 D:\Users\93062\Desktop\3.01\sys3.0\tools\law_rag_engine\vector 加载向量存储
2025-07-30 13:23:05 - faiss.loader - INFO - Loading faiss.
2025-07-30 13:23:06 - faiss.loader - INFO - Successfully loaded faiss.
2025-07-30 13:23:06 - LawRAGEngine - INFO - 法规向量存储初始化完成
2025-07-30 13:23:06 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-30 13:23:07 - smart_graph_loader - INFO - 🚀 开始智能图谱数据检查和加载
2025-07-30 13:23:09 - Neo4jConnection - INFO - 成功连接到Neo4j数据库
2025-07-30 13:23:09 - smart_graph_loader - INFO - ✅ 数据库连接成功
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: HazardBehavior)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:HazardBehavior)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: WarningSignal)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:WarningSignal)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: TriggerCause)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:TriggerCause)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - neo4j.notifications - WARNING - Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Damage)} {position: line: 2, column: 26, offset: 26} for query: '\n                MATCH (n:Damage)\n                WHERE n.embedding IS NOT NULL\n                RETURN count(n) as count\n                '
2025-07-30 13:23:09 - smart_graph_loader - INFO - 📊 文件未变化，无需更新
2025-07-30 13:23:09 - smart_graph_loader - INFO - 📋 更新检查结果: 数据库状态正常，无需更新
2025-07-30 13:23:09 - smart_graph_loader - INFO - ✅ 图谱数据已是最新，无需更新
2025-07-30 13:23:09 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:23:09 - Neo4jConnection - INFO - 所有数据库连接已关闭
2025-07-30 13:23:20 - opentelemetry.trace - WARNING - Overriding of current TracerProvider is not allowed
2025-07-30 13:23:23 - system - INFO - 系统信息: Python 3.12.3 | packaged by conda-forge | (main, Apr 15 2024, 18:20:11) [MSC v.1938 64 bit (AMD64)], OS: Windows
2025-07-30 13:23:23 - system - INFO - CrewAI框架版本已加载，系统将使用CrewAI执行工作
2025-07-30 13:23:23 - system - INFO - 初始化控制器智能体..
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - ControllerAgent - INFO - 智能体 控制者 初始化完成
2025-07-30 13:23:23 - system - INFO - 开始执行任务，任务ID: TASK-20250730-14, 模式: extract
2025-07-30 13:23:23 - ControllerAgent - INFO - 开始执行任务，模式: extract, 任务ID: TASK-20250730-14
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化智能体
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化收集器智能体...
2025-07-30 13:23:23 - weibo_api - INFO - 微博搜索API初始化完成
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - CollectorAgent - INFO - 智能体 数据收集者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化提取器智能体...
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - ExtractorAgent - INFO - 智能体 信息提取者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 初始化评估器智能体...
2025-07-30 13:23:23 - LLMClient - INFO - LLM客户端初始化完成，提供商: openai, 模型: qwen-plus
2025-07-30 13:23:23 - EvaluatorAgent - INFO - 智能体 风险评估者 初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 所有智能体初始化完成
2025-07-30 13:23:23 - ControllerAgent - INFO - 执行步骤: extract
2025-07-30 13:23:23 - ControllerAgent - INFO - 开始执行信息抽取工作流
2025-07-30 13:23:23 - ExtractorAgent - INFO - 开始执行信息提取任务
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文件加载微博数据: data/raw/test_sample.json
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文件加载了 10 条微博数据
2025-07-30 13:23:23 - ExtractorAgent - INFO - [信息抽取] |□□□□□□□□□□□□□□□□□□□□| 0% (0/10)
2025-07-30 13:23:23 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 39
2025-07-30 13:23:23 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:23:45 - openai._base_client - INFO - Retrying request to /chat/completions in 0.493820 seconds
2025-07-30 13:24:06 - openai._base_client - INFO - Retrying request to /chat/completions in 0.781729 seconds
2025-07-30 13:24:28 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:24:28 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:24:28 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:24:28 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:24:28 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:24:28 - ExtractorAgent - INFO - [信息抽取] |■■□□□□□□□□□□□□□□□□□□| 10% (1/10)
2025-07-30 13:24:28 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 950
2025-07-30 13:24:28 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:24:49 - openai._base_client - INFO - Retrying request to /chat/completions in 0.399846 seconds
2025-07-30 13:25:10 - openai._base_client - INFO - Retrying request to /chat/completions in 0.946423 seconds
2025-07-30 13:25:33 - root - ERROR - LiteLLM call failed: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:25:33 - ExtractorAgent - WARNING - LLM调用失败: litellm.Timeout: APITimeoutError - Request timed out. Error_str: Request timed out.
2025-07-30 13:25:33 - ExtractorAgent - INFO - LLM语义分析结果: {'should_extract': False, 'reason': 'LLM分析失败', 'confidence': 0.3}
2025-07-30 13:25:33 - ExtractorAgent - INFO - 语义预判断：跳过抽取 - LLM分析失败
2025-07-30 13:25:33 - ExtractorAgent - INFO - 语义预判断跳过，不进行重试: LLM分析失败
2025-07-30 13:25:33 - ExtractorAgent - INFO - [信息抽取] |■■■■□□□□□□□□□□□□□□□□| 20% (2/10)
2025-07-30 13:25:33 - ExtractorAgent - INFO - 从文本中提取建筑安全隐患信息，文本长度: 492
2025-07-30 13:25:33 - LiteLLM - INFO - 
LiteLLM completion() model= qwen-plus; provider = openai
2025-07-30 13:25:54 - openai._base_client - INFO - Retrying request to /chat/completions in 0.489342 seconds
