"""
简化的配置服务
统一的配置管理，替代原有的复杂配置架构
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class PathConfig(BaseModel):
    """路径配置"""
    project_root: str = Field(default=".", description="项目根目录")
    data_dir: str = Field(default="data", description="数据目录")
    logs_dir: str = Field(default="logs", description="日志目录")
    
    @property
    def raw_data_dir(self) -> Path:
        return Path(self.project_root) / self.data_dir / "raw"
    
    @property
    def structured_data_dir(self) -> Path:
        return Path(self.project_root) / self.data_dir / "structured"
    
    @property
    def evaluation_dir(self) -> Path:
        return Path(self.project_root) / self.data_dir / "evaluation"
    
    @property
    def reports_dir(self) -> Path:
        return Path(self.project_root) / self.data_dir / "reports"
    
    @property
    def status_dir(self) -> Path:
        return Path(self.project_root) / self.logs_dir / "status"

    @property
    def weibo_data_dir(self) -> Path:
        return Path(self.project_root) / self.data_dir / "raw" / "weibo"


class LLMConfig(BaseModel):
    """LLM配置"""
    provider: str = Field(default="openai", description="LLM提供商")
    model: str = Field(default="gpt-3.5-turbo", description="模型名称")
    api_key: str = Field(default="", description="API密钥")
    base_url: str = Field(default="", description="API基础URL")
    temperature: float = Field(default=0.7, description="温度参数")
    max_tokens: int = Field(default=2000, description="最大token数")


class SystemConfig(BaseModel):
    """系统配置"""
    debug_mode: bool = Field(default=False, description="调试模式")
    log_level: str = Field(default="INFO", description="日志级别")
    
    run_mode: Dict[str, Any] = Field(default_factory=lambda: {
        "default_mode": "full",
        "available_modes": {
            "collect": {"workflow": ["collect"], "description": "数据收集"},
            "extract": {"workflow": ["extract"], "description": "信息提取"},
            "evaluate": {"workflow": ["evaluate"], "description": "风险评估"},
            "full": {"workflow": ["collect", "extract", "evaluate", "report"], "description": "完整流程"},
            "report": {"workflow": ["report"], "description": "报告生成"},
            "check": {"workflow": ["check"], "description": "系统状态检查"}
        },
        "dependencies": {
            "extract": ["collect"],
            "evaluate": ["extract"],
            "report": ["evaluate"]
        },
        "param_rules": {
            "collect": {"required": [], "optional": ["topic", "location", "time_range", "max_posts", "keywords"]},
            "extract": {"required": [], "optional": ["input"]},
            "evaluate": {"required": [], "optional": ["input"]},
            "report": {"required": [], "optional": ["input", "report_path"]},
            "full": {"required": [], "optional": ["topic", "location", "time_range", "max_posts", "keywords"]},
            "check": {"required": [], "optional": []}
        }
    })


class DatabaseConfig(BaseModel):
    """数据库配置"""
    neo4j_uri: str = Field(default="bolt://localhost:7687", description="Neo4j连接URI")
    neo4j_user: str = Field(default="neo4j", description="Neo4j用户名")
    neo4j_password: str = Field(default="", description="Neo4j密码")


class KnowledgeGraphConfig(BaseModel):
    """知识图谱配置"""
    min_confidence: float = Field(default=0.8, description="最小置信度阈值")
    min_similarity: float = Field(default=0.8, description="最小相似度阈值")
    high_quality_threshold: float = Field(default=0.8, description="高质量路径阈值")
    basic_quality_threshold: float = Field(default=0.8, description="基本质量路径阈值")
    max_path_length: int = Field(default=5, description="最大路径长度")


class LawConfig(BaseModel):
    """法规配置"""
    embedding_model: str = Field(default="paraphrase-multilingual-MiniLM-L12-v2", description="嵌入模型")
    vector_store_path: str = Field(default="", description="向量存储路径")
    chunk_size: int = Field(default=500, description="文档分块大小")
    chunk_overlap: int = Field(default=50, description="分块重叠大小")
    rerank_threshold: float = Field(default=0.8, description="重排序相似度阈值")
    similarity_threshold: float = Field(default=0.7, description="向量检索相似度阈值")
    top_k: int = Field(default=5, description="返回结果数量")


class Config(BaseModel):
    """主配置类"""
    paths: PathConfig = Field(default_factory=PathConfig)
    llm: LLMConfig = Field(default_factory=LLMConfig)
    system: SystemConfig = Field(default_factory=SystemConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    law: LawConfig = Field(default_factory=LawConfig)
    knowledge_graph: KnowledgeGraphConfig = Field(default_factory=KnowledgeGraphConfig)


class SimpleConfigService:
    """简化的配置服务"""
    
    _instance = None
    
    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = SimpleConfigService()
        return cls._instance
    
    def __init__(self):
        """初始化配置服务"""
        self.config = Config()
        self.is_loaded = False
        # 设置项目根目录为sys3.0目录（config.py的上级目录）
        self.config.paths.project_root = str(Path(__file__).parent.parent)
    
    def load_config(self, config_file: Optional[str] = None) -> None:
        """加载配置"""
        # 如果已经加载过，则跳过
        if self.is_loaded:
            return

        # 加载.env文件
        try:
            from dotenv import load_dotenv
            env_path = Path(self.config.paths.project_root).parent / ".env"
            if env_path.exists():
                load_dotenv(dotenv_path=env_path)
                print(f"加载环境变量配置文件: {env_path}")
        except:
            pass

        # 从环境变量加载
        self._load_from_env()

        # 从文件加载（如果提供）
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                self._update_config(file_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")

        self.is_loaded = True

    def _load_from_env(self) -> None:
        """从环境变量加载配置"""
        # 简化的环境变量映射
        env_mappings = {
            # LLM配置 (字符串类型)
            "LLM_API_KEY": ("llm", "api_key"),
            "LLM_PROVIDER": ("llm", "provider"),
            "LLM_MODEL": ("llm", "model"),
            "LLM_API_BASE": ("llm", "base_url"),
            # LLM配置 (数值类型)
            "LLM_TEMPERATURE": ("llm", "temperature", float),
            "LLM_MAX_TOKENS": ("llm", "max_tokens", int),
            # 系统配置
            "LOG_LEVEL": ("system", "log_level"),
            # 数据库配置
            "NEO4J_URI": ("database", "neo4j_uri"),
            "NEO4J_USER": ("database", "neo4j_user"),
            "NEO4J_PASSWORD": ("database", "neo4j_password"),
        }

        for env_key, mapping in env_mappings.items():
            if value := os.getenv(env_key):
                section, attr = mapping[:2]
                converter = mapping[2] if len(mapping) > 2 else str
                try:
                    setattr(getattr(self.config, section), attr, converter(value))
                except:
                    pass

        # 特殊处理DEBUG_MODE
        if debug := os.getenv("DEBUG_MODE"):
            self.config.system.debug_mode = debug.lower() in ("true", "1", "yes")
    
    def _update_config(self, file_config: Dict[str, Any]) -> None:
        """从字典更新配置"""
        try:
            # 使用pydantic的model_validate来更新配置
            self.config = Config.model_validate({**self.config.model_dump(), **file_config})
        except Exception as e:
            print(f"更新配置失败: {e}")
    
    def get_config(self, key: str, default: Any = None, as_dict: bool = False, as_path: bool = False) -> Any:
        """获取配置值"""
        if not self.is_loaded:
            self.load_config()
        
        parts = key.split('.')
        value = self.config
        
        for part in parts:
            try:
                value = getattr(value, part)
            except AttributeError:
                return default

        if as_dict:
            return value.model_dump()

        if as_path and not isinstance(value, Path):
            return Path(str(value))
        
        return value
    
    def get_path(self, path_name: str) -> Path:
        """获取路径配置"""
        try:
            return getattr(self.config.paths, path_name)
        except AttributeError:
            return Path(self.config.paths.project_root) / path_name
    
    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        directories = [
            self.config.paths.raw_data_dir,
            self.config.paths.structured_data_dir,
            self.config.paths.evaluation_dir,
            self.config.paths.reports_dir,
            self.config.paths.status_dir,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def setup_llm_env(self) -> None:
        """设置LLM环境变量"""
        if self.config.llm.api_key:
            os.environ["LLM_API_KEY"] = self.config.llm.api_key
        if self.config.llm.base_url:
            os.environ["LLM_BASE_URL"] = self.config.llm.base_url
    
    def is_debug_mode(self) -> bool:
        """检查是否为调试模式"""
        return self.config.system.debug_mode

    def get_kg_config(self) -> Dict[str, Any]:
        """获取知识图谱配置"""
        return {
            "neo4j_uri": self.config.database.neo4j_uri,
            "neo4j_user": self.config.database.neo4j_user,
            "neo4j_password": self.config.database.neo4j_password,
            "min_confidence": self.config.knowledge_graph.min_confidence,
            "min_similarity": self.config.knowledge_graph.min_similarity,
            "high_quality_threshold": self.config.knowledge_graph.high_quality_threshold,
            "basic_quality_threshold": self.config.knowledge_graph.basic_quality_threshold,
            "max_path_length": self.config.knowledge_graph.max_path_length
        }

    def get_law_config(self) -> Dict[str, Any]:
        """获取法规配置"""
        return {
            "embedding_model": self.config.law.embedding_model,
            "vector_store_path": self.config.law.vector_store_path,
            "chunk_size": self.config.law.chunk_size,
            "chunk_overlap": self.config.law.chunk_overlap,
            "rerank_threshold": self.config.law.rerank_threshold,
            "similarity_threshold": self.config.law.similarity_threshold,
            "top_k": self.config.law.top_k
        }


# 创建全局配置服务实例
config_service = SimpleConfigService.get_instance()
