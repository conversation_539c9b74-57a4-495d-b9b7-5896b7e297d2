"""
LLM客户端模块，作为CrewAI框架的适配层
专注于配置和提供CrewAI兼容的LLM实例，特别支持siliconflow平台
"""

from typing import Dict, Any, Optional
import os

from crewai import LLM

from utils.exceptions import SystemException
from utils.logger import LoggerMixin

# 导入配置服务
from config.config import config_service

class LLMClient(LoggerMixin):
    """
    LLM客户端，作为CrewAI框架的适配层
    
    此模块专注于配置CrewAI框架使用的LLM实例，特别是支持siliconflow平台
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化LLM客户端
        
        Args:
            config: LLM配置，包含provider, api_key, model等
        """
        # 初始化日志
        super().__init__()
        
        # 如果没有提供配置，使用配置服务获取
        if config is None:
            config = config_service.get_config("llm", as_dict=True)

        # 确保config不为None
        self.config = config or {}
        self.provider = self.config.get("provider", "openai").lower()
        self.model = self.config.get("model", "gpt-4")
        self.api_key = self.config.get("api_key", "")
        self.temperature = self.config.get("temperature", 0.7)
        self.max_tokens = self.config.get("max_tokens", 1000)
        self.api_base = self.config.get("base_url", None)
        
        # 环境变量由config_service统一管理，无需重复设置
        
        # 初始化CrewAI兼容的LLM实例
        self.llm = self._initialize_crewai_llm()
        
        self.log_info(f"LLM客户端初始化完成，提供商: {self.provider}, 模型: {self.model}")
    

    
    def _initialize_crewai_llm(self) -> LLM:
        """
        初始化CrewAI兼容的LLM实例

        Returns:
            LLM: CrewAI兼容的LLM实例

        Raises:
            SystemException: LLM初始化失败
        """
        try:
            # 统一的OpenAI兼容调用逻辑
            # 所有兼容OpenAI的API都使用相同的调用方式

            # 使用openai/模型名的格式，这是LiteLLM的标准格式
            model_name = f"openai/{self.model}"

            # 创建CrewAI LLM实例，添加超时配置
            llm_params = {
                "model": model_name,
                "api_key": self.api_key,
                "base_url": self.api_base,  # 直接使用配置的base_url
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "timeout": 120,  # 设置120秒超时，适应阿里云千问API
                "max_retries": 3,  # 设置重试次数
                "request_timeout": 120  # 请求超时时间
            }

            return LLM(**llm_params)

        except Exception as e:
            self.log_error(f"初始化LLM失败: {str(e)}", e)
            raise SystemException(f"初始化LLM失败: {str(e)}")
    
    def get_llm(self) -> LLM:
        """
        获取CrewAI兼容的LLM实例
        
        Returns:
            LLM: CrewAI兼容的LLM实例
        """
        return self.llm


